{"compilerOptions": {"target": "es2022", "module": "node16", "lib": ["ES2022", "DOM"], "moduleResolution": "node16", "rootDir": ".", "outDir": "build", "allowSyntheticDefaultImports": true, "importHelpers": true, "alwaysStrict": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": false, "noImplicitThis": false, "strict": true, "strictNullChecks": true, "esModuleInterop": true, "baseUrl": "src", "skipLibCheck": true, "paths": {"#/*": ["./*"]}}, "include": ["src", "__tests__"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}